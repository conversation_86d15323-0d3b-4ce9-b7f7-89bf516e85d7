{"easycom": {"autoscan": true, "custom": {"^wd-(.*)": "wot-design-uni/components/wd-$1/wd-$1.vue", "^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)": "z-paging/components/z-paging$1/z-paging$1.vue"}}, "pages": [{"path": "pages/index/index", "aliasPath": "/index", "name": "index", "style": {"navigationStyle": "custom", "navigationBarTitleText": "主页"}, "type": "home"}, {"path": "pages/login/index", "aliasPath": "/login", "name": "login", "style": {"navigationStyle": "custom", "navigationBarTitleText": "登录"}}], "subPackages": [{"root": "pages/common", "pages": [{"path": "web-view/index", "aliasPath": "/web-view", "name": "web-view", "style": {"navigationBarTitleText": "web-view", "transparentTitle": "auto"}}, {"path": "rich-view/index", "aliasPath": "/rich-view", "name": "rich-view", "style": {"navigationBarTitleText": "rich-view"}}, {"path": "live-pusher/index", "aliasPath": "/live-pusher", "name": "live-pusher", "style": {"navigationStyle": "custom", "navigationBarTitleText": "live-pusher"}}]}, {"root": "pages/railway", "pages": [{"path": "overview/index", "aliasPath": "/railway-overview", "name": "railway-overview", "meta": {"middleware": []}, "style": {"navigationStyle": "custom", "navigationBarTitleText": "作业概览"}}, {"path": "steps/index", "aliasPath": "/railway-steps", "name": "railway-steps", "meta": {"middleware": []}, "style": {"navigationStyle": "custom", "navigationBarTitleText": "环节事项"}}, {"path": "result/index", "aliasPath": "/railway-result", "name": "railway-result", "meta": {"middleware": []}, "style": {"navigationStyle": "custom", "navigationBarTitleText": "完成事项"}}]}, {"root": "pages/vehicle", "pages": [{"path": "scanner/index", "aliasPath": "/vehicle-scanner", "name": "vehicle-scanner", "style": {"navigationStyle": "custom", "navigationBarTitleText": "扫描票号"}}, {"path": "inspect/index", "aliasPath": "/vehicle-inspect", "name": "vehicle-inspect", "style": {"navigationStyle": "custom", "navigationBarTitleText": "车辆检修"}}, {"path": "inspect-repeat/index", "aliasPath": "/vehicle-inspect-repeat", "name": "vehicle-inspect-repeat", "style": {"navigationStyle": "custom", "navigationBarTitleText": "车辆检修-人工上报"}}, {"path": "inspect-result/index", "aliasPath": "/vehicle-inspect-result", "name": "vehicle-inspect-result", "style": {"navigationStyle": "custom", "navigationBarTitleText": "车辆检修-结果"}}]}, {"root": "pages/warehouse", "pages": [{"path": "task/index", "aliasPath": "/warehouse-task", "name": "warehouse-task", "style": {"navigationStyle": "custom", "navigationBarTitleText": "料库任务列表"}}, {"path": "task/create", "aliasPath": "/warehouse-task-create", "name": "warehouse-task-create", "style": {"navigationStyle": "custom", "navigationBarTitleText": "新建料库任务"}}, {"path": "task/create-confirm", "aliasPath": "/warehouse-task-create-confirm", "name": "warehouse-task-create-confirm", "style": {"navigationStyle": "custom", "navigationBarTitleText": "新建料库任务确定"}}, {"path": "task/receive", "aliasPath": "/warehouse-task-receive", "name": "warehouse-task-receive", "style": {"navigationStyle": "custom", "navigationBarTitleText": "领取料库工具"}}, {"path": "task/receive-confirm", "aliasPath": "/warehouse-task-receive-confirm", "name": "warehouse-task-receive-confirm", "style": {"navigationStyle": "custom", "navigationBarTitleText": "领取料库工具确定"}}, {"path": "task/return", "aliasPath": "/warehouse-task-return", "name": "warehouse-task-return", "style": {"navigationStyle": "custom", "navigationBarTitleText": "归还料库工具"}}, {"path": "task/return-confirm", "aliasPath": "/warehouse-task-return-confirm", "name": "warehouse-task-return-confirm", "style": {"navigationStyle": "custom", "navigationBarTitleText": "归还料库工具确定"}}, {"path": "task/info", "aliasPath": "/warehouse-task-info", "name": "warehouse-task-info", "style": {"navigationStyle": "custom", "navigationBarTitleText": "料库任务详情"}}]}, {"root": "pages/assistant", "pages": [{"path": "index", "aliasPath": "/assistant", "name": "assistant", "style": {"navigationStyle": "custom", "navigationBarTitleText": "语音助手"}}]}, {"root": "pages/settings", "pages": [{"path": "index", "aliasPath": "/settings", "name": "settings", "style": {"navigationStyle": "custom", "navigationBarTitleText": "设置"}}]}], "globalStyle": {"navigationBarTitleText": "glasses-app", "navigationBarBackgroundColor": "#282E3B", "navigationBarTextStyle": "white", "backgroundColor": "#282E3B"}}