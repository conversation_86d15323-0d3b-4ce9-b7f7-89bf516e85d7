<script setup>
const props = defineProps({
  hiddenHeaderLeft: {
    type: Boolean,
    default: false,
  },
  hiddenHeaderSetting: {
    type: Boolean,
    default: false,
  },
})
/** 修复 APP 端 useNow 兼容性问题 */
function useNowFix({ interval = 0 } = {}) {
  const date = ref(String(new Date()))

  setInterval(() => {
    date.value = String(new Date())
  }, interval)

  return date
}

const nowDate = useDateFormat(useNowFix({ interval: 1000 }), 'MM-DD HH:mm:ss')

const userStore = useUserStore()

const userName = computed(()=> userStore.workerInfo?.userName)

async function onLoginToggleClick() {
  if (userName) {
    const ret = await uni.showModal({
      title: '提示',
      content: '确定要退出登录吗',
      showCancel: true,
    })

    if (ret.confirm) {
      userStore.logout({ toLogin: true })
    }
    return false
  }

  uni.reLaunch({ url: '/pages/login/index' })
}

function onSettingClick() {
  uni.navigateTo({ url: '/pages/settings/index' })
}
</script>

<template>
  <view class="h-6 w-full flex px-2 text-slate-400">
    <view v-if="!hiddenHeaderLeft" class="w-24 flex-none flex items-center">
      <slot name="left">
        <text class="text-xs">
          {{ nowDate }}
        </text>
      </slot>
    </view>

    <view class="w-full flex-1 overflow-hidden h-full">
      <slot></slot>
    </view>

    <view class="w-24 flex flex-none items-center justify-end space-x-1" >
      <text class="text-xs" :aria-label="userName ? '退出登录' : '打开登录'" @click="onLoginToggleClick">
        {{ userName || '未登录' }}
      </text>
      <view v-if="!hiddenHeaderSetting && userName" class="h-[22rpx] w-[1rpx] bg-slate-600"></view>
      <view v-if="!hiddenHeaderSetting && userName" class="flex items-center text-xs" aria-label="设置" @click="onSettingClick">
        <text>设置</text>
        <view class="i-carbon-chevron-right size-3"></view>
      </view>
    </view>
  </view>
</template>
