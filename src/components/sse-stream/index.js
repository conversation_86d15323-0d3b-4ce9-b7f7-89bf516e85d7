/**
 * 基于 renderjs + SSE 的语音助手流式处理类
 * 提供与现有 VoiceAssistantStream 相同的接口，实现无缝替换
 */

export class VoiceAssistantStreamRenderjs {
  constructor(options = {}) {
    const defaultOptions = {
      baseUrl: process.env.VITE_API_COPILOT,
      headers: {
        Authorization: 'Bearer ragflow-E1ODg0MDJhNzRjZTExZjA4ZWU2MDI0Mm',
      },
    }

    this.options = {
      ...defaultOptions,
      ...options,
    }

    this.baseApi = `${this.options.baseUrl}/api/v1/chats/${this.options.dialogId}`
    this.sessionId = null
    this.sseComponent = null
    this.isInitialized = false

    // 初始化会话ID
    this.getSessionId()
  }

  // 设置 SSE 组件引用
  setSSEComponent(component) {
    this.sseComponent = component
    this.isInitialized = true
  }

  // 获取会话ID
  async getSessionId() {
    if (this.sessionId) {
      return this.sessionId
    }

    try {
      const res = await uni.request({
        url: `${this.baseApi}/sessions`,
        method: 'POST',
        header: this.options.headers,
        data: {
          name: 'new session',
        },
      })

      this.sessionId = res?.data?.data?.id
      return this.sessionId
    }
    catch (error) {
      console.error('获取会话ID失败:', error)
      throw error
    }
  }

  // 开始对话流 - 与现有接口保持一致
  async startConversation(options = {}) {
    if (!this.isInitialized || !this.sseComponent) {
      console.error('SSE 组件未初始化')
      if (options.onError) {
        options.onError(new Error('SSE 组件未初始化'))
      }
      return
    }

    const {
      sessionId,
      stream = true,
      onMessage,
      onError,
      onComplete,
    } = options

    const question = `${options.question} /no_think`

    try {
      // 确保有会话ID
      const currentSessionId = sessionId || this.sessionId || await this.getSessionId()

      // 调用 SSE 组件的方法
      this.sseComponent.startConversation({
        question,
        sessionId: currentSessionId,
        stream,
        onMessage: (data) => {
          // 处理消息数据，保持与现有格式兼容
          if (data.answer) {
            data.answer = this.replaceAnswer(data.answer)
          }

          const messageData = {
            answer: data.answer || '',
            reference: data.reference || {},
            audioBinary: data.audioBinary || null,
            sessionId: data.sessionId || '',
            isComplete: false,
            rawData: data,
          }

          if (messageData.sessionId) {
            this.sessionId = messageData.sessionId
          }

          if (onMessage) {
            onMessage(messageData)
          }
        },
        onError: (error) => {
          console.error('Stream error:', error)
          if (onError) {
            onError(error)
          }
        },
        onComplete: () => {
          console.log('Stream completed')
          if (onComplete) {
            onComplete()
          }
        },
      })
    }
    catch (error) {
      console.error('启动对话流失败:', error)
      if (onError) {
        onError(error)
      }
    }
  }

  // 停止当前对话流
  stopConversation() {
    if (this.sseComponent) {
      this.sseComponent.stopConversation()
    }
  }

  // 检查是否正在进行对话
  isActive() {
    return this.sseComponent ? this.sseComponent.isStreamActive() : false
  }

  // 替换答案中的特定内容（保持与现有逻辑一致）
  replaceAnswer(text) {
    const keywords = ['ID', 'id', '文档', 'Document']

    // 检查输入是否有效
    if (typeof text !== 'string') {
      console.error('错误：输入文本必须是字符串。')
      return text
    }

    if (!Array.isArray(keywords)) {
      console.error('错误：关键字必须是一个数组。')
      return text
    }

    const regex = /[(（](.*?)[)）]/g
    const lowerCaseKeywords = keywords.map(k => k.toLowerCase())

    return text.replace(regex, (match, innerContent) => {
      const hasKeyword = lowerCaseKeywords.some(keyword =>
        innerContent.toLowerCase().includes(keyword),
      )
      return hasKeyword ? '' : match
    })
  }

  // 静态方法，保持与现有接口一致
  static replaceAnswer(text) {
    const instance = new VoiceAssistantStreamRenderjs()
    return instance.replaceAnswer(text)
  }
}

export default VoiceAssistantStreamRenderjs
