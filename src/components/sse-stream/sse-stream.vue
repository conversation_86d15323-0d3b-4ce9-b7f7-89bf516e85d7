<script>
export default {
  name: 'SSEStream',
  props: {
    // 配置选项
    options: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isActive: false,
      currentSessionId: null,
      actionData: null,
    }
  },
  methods: {
    // 开始对话流
    startConversation(params) {
      const {
        question,
        sessionId,
        stream = true,
        onMessage,
        onError,
        onComplete,
      } = params

      // 存储回调函数
      this.callbacks = {
        onMessage,
        onError,
        onComplete,
      }

      this.isActive = true
      this.currentSessionId = sessionId

      // 通过 actionData 触发 renderjs 方法
      this.actionData = {
        type: 'start',
        timestamp: Date.now(),
        params: {
          question,
          sessionId: sessionId || this.currentSessionId,
          stream,
        },
      }
    },

    // 停止对话流
    stopConversation() {
      this.isActive = false
      this.actionData = {
        type: 'stop',
        timestamp: Date.now(),
      }
    },

    // 检查是否正在进行对话
    isStreamActive() {
      return this.isActive
    },

    // 获取会话ID
    async getSessionId() {
      if (this.currentSessionId) {
        return this.currentSessionId
      }

      try {
        const baseApi = `${this.options.baseUrl}/api/v1/chats/${this.options.dialogId}`
        const res = await uni.request({
          url: `${baseApi}/sessions`,
          method: 'POST',
          header: this.options.headers,
          data: {
            name: 'new session',
          },
        })

        this.currentSessionId = res?.data?.data?.id
        return this.currentSessionId
      }
      catch (error) {
        console.error('获取会话ID失败:', error)
        throw error
      }
    },

    // 处理来自 renderjs 的消息
    handleMessage(data) {
      if (this.callbacks?.onMessage) {
        // 处理消息数据，保持与现有格式兼容
        const messageData = {
          answer: data.answer || '',
          reference: data.reference || {},
          audioBinary: data.audio_binary || null,
          sessionId: data.session_id || '',
          isComplete: false,
          rawData: data,
        }

        if (messageData.sessionId) {
          this.currentSessionId = messageData.sessionId
        }

        this.callbacks.onMessage(messageData)
      }
    },

    // 处理来自 renderjs 的错误
    handleError(error) {
      this.isActive = false
      if (this.callbacks?.onError) {
        this.callbacks.onError(error)
      }
    },

    // 处理来自 renderjs 的完成事件
    handleComplete() {
      this.isActive = false
      if (this.callbacks?.onComplete) {
        this.callbacks.onComplete()
      }
    },

    // 替换答案中的特定内容（保持与现有逻辑兼容）
    replaceAnswer(text) {
      const keywords = ['ID', 'id', '文档', 'Document']

      if (typeof text !== 'string') {
        return text
      }

      const regex = /[(（](.*?)[)）]/g
      const lowerCaseKeywords = keywords.map(k => k.toLowerCase())

      return text.replace(regex, (match, innerContent) => {
        const hasKeyword = lowerCaseKeywords.some(keyword =>
          innerContent.toLowerCase().includes(keyword),
        )
        return hasKeyword ? '' : match
      })
    },
  },
}
</script>

<script module="sseModule" lang="renderjs">
export default {
  data() {
    return {
      currentController: null,
      isRunning: false,
      currentOptions: {}
    }
  },
  methods: {
    // 更新配置选项
    updateOptions(newValue, oldValue, ownerInstance, instance) {
      this.currentOptions = newValue || {}
    },

    // 处理动作
    handleAction(newValue, oldValue, ownerInstance, instance) {
      if (!newValue || !newValue.type) return

      switch (newValue.type) {
        case 'start':
          this.startSSEStream(newValue.params)
          break
        case 'stop':
          this.stopSSEStream()
          break
      }
    },
    // 开始 SSE 流
    async startSSEStream(params) {
      // 停止当前流（如果存在）
      this.stopSSEStream()

      const { question, sessionId, stream = true } = params || {}

      const currentOptions = this.currentOptions

      try {
        const baseApi = `${currentOptions.baseUrl}/api/v1/chats/${currentOptions.dialogId}`
        const url = `${baseApi}/completions`

        const requestData = {
          session_id: sessionId,
          question: `${question} /no_think`,
          stream
        }

        // 使用 fetch API 进行 SSE 请求
        this.currentController = new AbortController()

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'text/event-stream',
            ...currentOptions.headers
          },
          body: JSON.stringify(requestData),
          signal: this.currentController.signal
        })

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }

        this.isRunning = true
        await this.processSSEResponse(response)

      } catch (error) {
        console.error('SSE Stream error:', error)
        this.isRunning = false
        this.$ownerInstance.callMethod('handleError', {
          message: error.message || '请求失败'
        })
      }
    },

    // 处理 SSE 响应
    async processSSEResponse(response) {
      const reader = response.body.getReader()
      const decoder = new TextDecoder()
      let buffer = ''

      try {
        while (this.isRunning) {
          const { done, value } = await reader.read()

          if (done) {
            this.$ownerInstance.callMethod('handleComplete')
            break
          }

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || '' // 保留不完整的行

          for (const line of lines) {
            this.processLine(line.trim())
          }
        }
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('处理 SSE 响应时出错:', error)
          this.$ownerInstance.callMethod('handleError', {
            message: error.message || '处理响应失败'
          })
        }
      } finally {
        this.isRunning = false
      }
    },

    // 处理单行数据
    processLine(line) {
      if (!line || !this.isRunning) return

      const dataPrefix = 'data:'

      if (line.startsWith(dataPrefix)) {
        const data = line.substring(dataPrefix.length).trim()

        // 检查是否为结束标志
        if (data === 'true' || data === '[DONE]') {
          this.$ownerInstance.callMethod('handleComplete')
          return
        }

        try {
          const jsonData = JSON.parse(data)
          this.handleSSEMessage(jsonData)
        } catch (error) {
          console.warn('Failed to parse JSON:', data, error)
        }
      }
    },

    // 处理 SSE 消息
    handleSSEMessage(data) {
      if (data.code === 0 && data.data) {
        // 处理答案文本
        if (data.data.answer) {
          data.data.answer = this.replaceAnswer(data.data.answer)
        }

        this.$ownerInstance.callMethod('handleMessage', data.data)
      } else if (data.code !== 0) {
        this.$ownerInstance.callMethod('handleError', {
          message: `API Error: ${data.code}`
        })
      }
    },

    // 停止 SSE 流
    stopSSEStream() {
      this.isRunning = false

      if (this.currentController) {
        this.currentController.abort()
        this.currentController = null
      }
    },

    // 替换答案中的特定内容
    replaceAnswer(text) {
      const keywords = ['ID', 'id', '文档', 'Document']

      if (typeof text !== 'string') {
        return text
      }

      const regex = /[(（](.*?)[)）]/g
      const lowerCaseKeywords = keywords.map(k => k.toLowerCase())

      return text.replace(regex, (match, innerContent) => {
        const hasKeyword = lowerCaseKeywords.some(keyword =>
          innerContent.toLowerCase().includes(keyword)
        )
        return hasKeyword ? '' : match
      })
    }
  }
}
</script>

<template>
  <view
    class="sse-stream-container"
    style="display: none;"
    :prop-options="options"
    :change:prop-options="sseModule.updateOptions"
    :prop-action="actionData"
    :change:prop-action="sseModule.handleAction"
  >
    <!-- 隐藏的容器，用于承载 renderjs -->
  </view>
</template>

<style scoped>
.sse-stream-container {
  position: absolute;
  top: -9999px;
  left: -9999px;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}
</style>
