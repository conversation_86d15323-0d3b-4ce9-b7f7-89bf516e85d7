# SSE Stream 组件

基于 uni-app + renderjs + SSE 的流式数据处理组件，用于替代现有的 VoiceAssistantStream 实现。

## 功能特性

- ✅ 使用 renderjs 调用浏览器原生 SSE API
- ✅ 支持流式消息接收
- ✅ 与现有 VoiceAssistantStream 接口完全兼容
- ✅ 支持错误处理和完成回调
- ✅ 自动会话管理
- ✅ 消息内容过滤（移除文档ID等信息）

## 组件结构

```
src/components/sse-stream/
├── sse-stream.vue     # 主组件（包含 renderjs 实现）
├── index.js           # 包装类（提供兼容接口）
└── README.md          # 说明文档
```

## 使用方法

### 1. 在页面中引入组件

```vue
<template>
  <!-- 其他内容 -->
  
  <!-- SSE 流处理组件 -->
  <SSEStream
    ref="sseStreamRef"
    :options="{
      baseUrl: process.env.VITE_API_COPILOT,
      dialogId: 'your-dialog-id',
      headers: {
        Authorization: 'Bearer your-token'
      }
    }"
  />
</template>

<script setup>
import { VoiceAssistantStreamRenderjs } from '@/components/sse-stream/index.js'
import SSEStream from '@/components/sse-stream/sse-stream.vue'

// 创建流处理实例
const voiceAssistantStream = new VoiceAssistantStreamRenderjs({
  dialogId: 'your-dialog-id'
})

// SSE 组件引用
const sseStreamRef = ref(null)

onMounted(async () => {
  await nextTick()
  if (sseStreamRef.value) {
    voiceAssistantStream.setSSEComponent(sseStreamRef.value)
  }
})
</script>
```

### 2. 使用流处理功能

```javascript
// 开始对话流
voiceAssistantStream.startConversation({
  question: '你好，请介绍一下自己',
  stream: true,
  onMessage: (messageData) => {
    console.log('收到消息:', messageData.answer)
    // 处理流式消息
  },
  onError: (error) => {
    console.error('流处理错误:', error)
  },
  onComplete: () => {
    console.log('流处理完成')
  }
})

// 停止对话流
voiceAssistantStream.stopConversation()

// 检查是否正在进行对话
const isActive = voiceAssistantStream.isActive()
```

## API 接口

### VoiceAssistantStreamRenderjs

#### 构造函数

```javascript
new VoiceAssistantStreamRenderjs(options)
```

**参数:**
- `options.baseUrl` - API 基础URL
- `options.dialogId` - 对话ID
- `options.headers` - 请求头

#### 方法

##### setSSEComponent(component)
设置 SSE 组件引用

**参数:**
- `component` - SSE 组件实例

##### startConversation(options)
开始对话流

**参数:**
- `options.question` - 问题内容
- `options.sessionId` - 会话ID（可选）
- `options.stream` - 是否流式输出（默认 true）
- `options.onMessage` - 消息回调函数
- `options.onError` - 错误回调函数
- `options.onComplete` - 完成回调函数

##### stopConversation()
停止当前对话流

##### isActive()
检查是否正在进行对话

**返回值:** `boolean`

## 技术实现

### renderjs 部分

- 使用浏览器原生 `fetch` API 处理 SSE 请求
- 支持 `AbortController` 进行请求取消
- 自动解析 SSE 数据格式
- 通过 `$ownerInstance.callMethod()` 与 Vue 组件通信
- 使用 `:change:prop-action` 监听动作变化来触发方法

### Vue 组件部分

- 提供与现有 VoiceAssistantStream 相同的接口
- 处理会话管理和消息格式化
- 支持错误处理和状态管理
- 通过 `actionData` 响应式数据与 renderjs 通信

### 通信机制

根据 uni-app renderjs 的最佳实践：

1. **Service 层到 renderjs**: 通过 `:prop-xxx` 绑定数据，`:change:prop-xxx` 监听变化
2. **renderjs 到 Service 层**: 通过 `this.$ownerInstance.callMethod()` 调用方法
3. **获取 Service 层数据**: 通过 `this.$ownerInstance.$vm.xxx` 访问

## 兼容性说明

- ✅ 完全兼容现有 VoiceAssistantStream 接口
- ✅ 支持现有的消息格式和回调机制
- ✅ 保持现有的错误处理逻辑
- ✅ 支持现有的消息内容过滤功能

## 注意事项

1. 组件需要在支持 renderjs 的环境中运行（App 端和 H5 端）
2. 确保在组件挂载后再调用 `setSSEComponent` 方法
3. 组件会自动处理会话管理，无需手动管理会话ID
4. 支持多次调用 `startConversation`，会自动停止之前的流
5. renderjs 中必须通过 `:prop-xxx` 和 `:change:prop-xxx` 进行通信
6. 在 renderjs 中访问 Service 层数据需要使用 `this.$ownerInstance.$vm.xxx`

## 故障排除

### 常见问题

1. **组件未初始化错误**
   - 确保在 `onMounted` 或 `nextTick` 后调用 `setSSEComponent`

2. **SSE 连接失败**
   - 检查网络连接和 API 配置
   - 确认 Authorization token 是否正确

3. **消息接收异常**
   - 检查 `onMessage` 回调函数是否正确设置
   - 查看浏览器控制台是否有错误信息

## 测试

组件包含一个测试页面 `test.vue`，可以用来验证 SSE 功能：

```bash
# 在项目中引入测试组件
import TestSSE from '@/components/sse-stream/test.vue'
```

测试页面提供了：
- 问题输入框
- 开始/停止按钮
- 实时状态显示
- 错误信息显示
- 流式回答展示

通过测试页面可以验证：
- SSE 连接是否正常
- 流式数据接收是否正确
- 错误处理是否有效
- 停止功能是否工作
