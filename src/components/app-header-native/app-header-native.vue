<script setup>
/** 修复 APP 端 useNow 兼容性问题 */
function useNowFix({ interval = 0 } = {}) {
  const date = ref(String(new Date()))

  setInterval(() => {
    date.value = String(new Date())
  }, interval)

  return date
}

const nowDate = useDateFormat(useNowFix({ interval: 1000 }), 'MM-DD HH:mm:ss')

const userStore = ref({})

const userName = computed(() => userStore.value.userInfo?.loginWorkerInfoVo?.userName)

onLoad(() => {
  userStore.value = JSON.parse(uni.getStorageSync('user') || '{}')
})

async function onLoginToggleClick() {
  if (userName.value) {
    const ret = await uni.showModal({
      title: '提示',
      content: '确定要退出登录吗',
      showCancel: true,
    })

    if (ret.confirm) {
      uni.removeStorageSync('user')
      uni.reLaunch({ url: '/pages/login/index' })
    }
    return false
  }

  uni.reLaunch({ url: '/pages/login/index' })
}

function onSettingClick() {
  uni.navigateTo({ url: '/pages/settings/index' })
}
</script>

<template>
  <view class="app-header-native">
    <view class="app-header-native__left">
      <slot name="left">
        <text class="app-header-native__left__text">
          {{ nowDate }}
        </text>
      </slot>
    </view>

    <view class="app-header-native__center">
      <slot></slot>
    </view>

    <view class="app-header-native__right" :arid-label="userName ? '退出登录' : '打开登录'" @click="onLoginToggleClick">
      <text class="app-header-native__right__text">
        {{ userName || '未登录' }}
      </text>

      <!-- <view v-if="userName" class="app-header-native__right__divider"></view> -->

      <!-- <view v-if="userName" class="app-header-native__right__setting" aria-label="设置" @click="onSettingClick">
        <text class="app-header-native__right__setting-text">
          设置
        </text>
        <image
          src="~@assets/images/index/carbon-right.png"
          mode="aspectFit"
          class="app-header-native__right__icon"
        />
      </view> -->
    </view>
  </view>
</template>

<style lang="postcss">
.app-header-native {
  flex-direction: row;
  align-items: center;
  padding-left: 16rpx;
  padding-right: 16rpx;
  height: 48rpx;
  background-color: rgba(0, 0, 0, 0.7);
}

.app-header-native__left {
  width: 192rpx;
}

.app-header-native__left__text {
  font-size: 24rpx;
  color: #94a3b8;
}

.app-header-native__center {
  flex: 1;
  overflow: hidden;
}

.app-header-native__right {
  width: 192rpx;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
}

.app-header-native__right__icon {
  height: 24rpx;
  width: 24rpx;
}

.app-header-native__right__text {
  font-size: 24rpx;
  color: #94a3b8;
}

.app-header-native__right__divider {
  width: 1rpx;
  height: 22rpx;
  background-color: #444c5d;
  margin-left: 12rpx;
  margin-right: 12rpx;
}

.app-header-native__right__setting {
  flex-direction: row;
  align-items: center;
  justify-content: end;
}

.app-header-native__right__setting-text {
  font-size: 24rpx;
  color: #94a3b8;
}
</style>
