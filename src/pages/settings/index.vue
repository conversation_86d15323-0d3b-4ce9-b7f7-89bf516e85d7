<script setup>
import { postVehicleTaskEnabled } from '@/api/vehicle/index.js'

const formInfo = ref({
  broadcastStatus: 0,
  promptStatus: 0,
})

const userStore = useUserStore()

const model = computed(() => {
  return [
    {
      label: '开启播报',
      closeLabel: '关闭播报',
      icon: 'i-carbon-volume-up',
      value: formInfo.value.broadcastStatus === 1,
      toggle: () => {
        formInfo.value.broadcastStatus = formInfo.value.broadcastStatus === 1 ? 0 : 1
        userStore.workerInfo.broadcastStatus = formInfo.value.broadcastStatus

        postVehicleTaskEnabled({
          broadcastStatus: formInfo.value.broadcastStatus,
        }).send(true)
      },
    },
    {
      label: '开启提示',
      closeLabel: '关闭提示',
      icon: 'i-carbon-light',
      value: formInfo.value.promptStatus === 1,
      toggle: () => {
        formInfo.value.promptStatus = formInfo.value.promptStatus === 1 ? 0 : 1
        userStore.workerInfo.promptStatus = formInfo.value.promptStatus

        postVehicleTaskEnabled({
          promptStatus: formInfo.value.promptStatus,
        }).send(true)
      },
    },
  ]
})

onMounted(() => {
  formInfo.value = {
    broadcastStatus: userStore.workerInfo.broadcastStatus,
    promptStatus: userStore.workerInfo.promptStatus,
  }
})

async function onLogoutClick() {
  const ret = await uni.showModal({
    title: '提示',
    content: '确定要退出登录吗',
    showCancel: true,
  })

  if (ret.confirm) {
    userStore.logout({ toLogin: true })
  }
}
</script>

<template>
  <app-layout hidden-header-setting>
    <template #content-top>
      <view class="text-center text-sm opacity-90">
        设置
      </view>
    </template>

    <view class="grid grid-cols-2 gap-2 mt-4">
      <view
        v-for="(item, index) of model"
        :key="index" class="flex items-center px-4 py-3 rounded space-x-1"
        :class="item.value ? 'bg-primary-500' : 'bg-[#282E3B]'"
        :aria-label="item.value ? item.closeLabel : item.label"
        @click="item.toggle"
      >
        <view :class="item.icon" class="size-4 flex-none"></view>
        <view class="text-xs flex-1 w-0 truncate">
          {{ item.value ? item.closeLabel : item.label }}
        </view>
      </view>
    </view>

    <view class="flex justify-center">
      <view class="bg-[#282E3B] text-[#A8B2C9] mt-4 text-center inline-block mx-auto px-4 py-1 text-sm rounded-sm" @click="onLogoutClick">
        退出登录
      </view>
    </view>
  </app-layout>
</template>

<style>
</style>
