<script>
import tempImage from '~@assets/images/contact/image-banner.jpg'

import iconFix from '~@assets/images/vehicle/icon-fix.png'
import iconImage from '~@assets/images/vehicle/icon-image.png'
import iconWarn from '~@assets/images/vehicle/icon-warn.png'

import { getVehicleItemGetCurrentInfo, postVehicleItemUpdate, postVehicleTaskDone, postVehicleTaskEnabled } from '@/api/vehicle/index.js'
import { useXfyunTTS } from '@/hooks/useXfyunTTS/index.js'
import { getStoreQuery, sleep } from '@/utils'
import InspectDiagram from './components/inspect-diagram/index.vue'
import InspectGuide from './components/inspect-guide/index.vue'
import InspectPoint from './components/inspect-point/index.vue'
import InspectAbnormal from './components/inspect-abnormal/index.vue'

export default {
  components: {
    InspectGuide,
    InspectDiagram,
    InspectPoint,
    InspectAbnormal,
  },
  setup() {
    const tts = useXfyunTTS()

    return {
      tts,
    }
  },
  data() {
    return {
      currentStack: '',
      inspectInfo: null,
      userStore: null,
      nextCountdown: 0,
      // TTS 队列相关状态
      isTransitioning: false, // 是否正在切换检查项
      pendingMessages: [], // 等待播放的新检查项消息
      pushing: false,
    }
  },
  computed: {
    prompts() {
      const value = []

      if (['inspectDiagram'].includes(this.currentStack)) {
        value.push({
          label: '关闭示意图',
          action: () => {
            this.$refs.inspectDiagramRef.close()
            this.currentStack = ''
          },
        })
      }
      else if (['inspectGuide'].includes(this.currentStack)) {
        if (this.inspectInfo?.broadcastStatus === 1) {
          value.push({
            label: '关闭播报',
            action: () => {
              this.handleGuideSwitch({
                broadcastStatus: 0,
                promptStatus: 0,
              })

              this.tts.stopPlay()
              this.currentStack = ''
              this.$refs.inspectGuideRef.close()
            },
          })
        }

        if (this.inspectInfo?.promptStatus === 1) {
          value.push({
            label: '关闭提示',
            action: () => {
              this.handleGuideSwitch({
                promptStatus: 0,
              })

              this.currentStack = ''
              this.$refs.inspectGuideRef.close()
            },
          })
        }
      }
      else if (['inspectPoint'].includes(this.currentStack)) {
        value.push({
          label: '关闭检修点',
          action: () => {
            this.$refs.inspectPointRef.close()
            this.currentStack = ''
          },
        })
      }
      else if(['inspectAbnormal'].includes(this.currentStack)) {
        value.push({
          label: '确认',
          type: 'primary',
          action: () => {
            this.$refs.inspectAbnormalRef.close()
            this.currentStack = ''
            
            const socketMessage = this.$refs.inspectAbnormalRef.socketMessage

            uni.$emit('socket-send', {
              uri: socketMessage.uri,
              token: this.userStore.userInfo.apiAccessToken,
              params: { ...socketMessage },
            })
          },
        })

        value.push({
          label: '取消',
          action: () => {
            this.$refs.inspectAbnormalRef.close()
            this.currentStack = ''
          },
        })
      }
      else {
        if (this.inspectInfo?.broadcastStatus !== 1) {
          value.push({
            label: '开启播报',
            action: () => {
              this.handleGuideSwitch({
                broadcastStatus: 1,
                promptStatus: 1,
              })
            },
          })
        }

        if (this.inspectInfo?.promptStatus !== 1) {
          value.push({
            label: '开启提示',
            action: () => {
              this.handleGuideSwitch({
                promptStatus: 1,
              })
            },
          })
        }

        if(this.inspectInfo?.last) {
          value.push({
            label: '完成任务',
            action: () => {
              this.handleTaskDone()
            },
          })
        } else {
          value.push({
            label: '强制结束',
            action: () => {
              this.handleForcedStop()
            },
          })
        }
      }

      return value
    },
    actionModel() {
      const value = [
        {
          icon: iconImage,
          label: '示意图',
          click: () => {
            this.currentStack = 'inspectDiagram'
            this.$refs.inspectDiagramRef.open(tempImage)
          },
        },
        {
          icon: iconFix,
          label: '检修点',
          click: () => {
            this.currentStack = 'inspectPoint'
            this.$refs.inspectPointRef.open()
          },
        },
        {
          icon: iconWarn,
          label: '人工上报',
          color: '#E4544A',
          click: () => {
            this.$refs.pusherRef.stop()
            uni.navigateTo({
              url: `/pages/vehicle/inspect-repeat/index?taskId=${this.inspectInfo.taskId}&sceneId=${this.inspectInfo.id}`,
            })
          },
        },
      ]

      return value
    },
    pusherPath() {
      if (!this.inspectInfo?.workTicketNo) {
        return ''
      }

      const value = `/${this.inspectInfo.workTicketNo}`

      return value
    },

    inspectTitle() {
      let value = ''

      if (this.inspectInfo?.itemNum) {
        value += this.inspectInfo.itemNum
      }

      return value
    },
    currentItemId() {
      return this.inspectInfo?.itemNum
    },
  },
  onLoad(options) {
    if (options.resultId) {
      this.inspectInfo = getStoreQuery(options.resultId)
    }
  },
  async onReady() {
    this.userStore = JSON.parse(uni.getStorageSync('user') || '{}')

    await this.$nextTick()
    this.$refs?.pusherRef?.init?.(this)
    this.$refs?.pusherRef?.startPreview?.()

    this.$watch('pusherPath', (value) => {
      if (!value) {
        return false
      }

      this.$refs?.pusherRef?.start?.()
    }, {
      immediate: true,
    })

    if (!this.inspectInfo?.workTicketNo) {
      await this.getInspectData()
    }

    this.onInspectGuide()

    uni.$emit('socket-send', {
      uri: 'vehicleConsumer',
      token: this.userStore.userInfo.apiAccessToken,
      params: { workTicketNo: this.inspectInfo.workTicketNo },
    })

    uni.$on('socket-message', this.onSocketMessage)

    // this.onSocketMessage({
    //   code: '201',
    //   message: '警告, 螺丝未拧紧',
    //   debugImageUrl: this.inspectInfo.itemImg
    // })
  },
  onShow() {
    this.$refs?.pusherRef?.restart()
  },
  onUnload() {
    clearInterval(this.timer)
    // 清理TTS队列相关状态
    this.pendingMessages = []
    this.isTransitioning = false
    this.tts.clearQueue()
    uni.$off('socket-message', this.onSocketMessage)
  },
  methods: {
    async handleGuideSwitch(params = {}) {
      if (typeof params.broadcastStatus === 'number') {
        this.inspectInfo.broadcastStatus = params.broadcastStatus
      }

      if (typeof params.promptStatus === 'number') {
        this.inspectInfo.promptStatus = params.promptStatus
      }

      uni.showToast({
        title: '操作成功',
        icon: 'none',
      })

      await postVehicleTaskEnabled({
        ...params,
        taskId: this.inspectInfo.taskId,
      }).send(true)
    },
    // TTS 队列管理方法（使用增强的TTS系统）
    addToTtsQueue(message, itemId) {
      // 使用增强的TTS系统的队列功能
      this.tts.speak(message, {
        toast: true,
        priority: 'enqueue',
        itemId, // 保存itemId用于跟踪
        onCompleted: () => {
          // 检查是否可以执行切换检查项
          this.checkTransitionReady()
        },
      })
    },

    // 检查当前itemId的消息是否全部播放完成
    hasCurrentItemMessages() {
      const queueStatus = this.tts.getQueue()
      return queueStatus.queue.some(item => item.config.itemId === this.currentItemId)
        || (queueStatus.currentPlaying && queueStatus.currentPlaying.config.itemId === this.currentItemId)
    },

    // 检查是否可以执行检查项切换
    checkTransitionReady() {
      if (this.isTransitioning && !this.hasCurrentItemMessages()) {
        // 当前检查项的消息已全部播放完成，可以执行切换
        this.executeTransition()
      }
    },

    // 执行检查项切换
    executeTransition() {
      this.isTransitioning = false
      this.actualHandleNext()
    },

    onInspectGuide() {
      if (!this.inspectTitle) {
        return false
      }

      const broadcastStatus = this.inspectInfo.broadcastStatus === 1
      const promptStatus = this.inspectInfo.promptStatus === 1

      if (broadcastStatus) {
        const speakText = `检修项 ${this.inspectInfo.number},` + this.inspectInfo.taskPoints.map((item, index) => `${index + 1} ${item.pointName}`).join(';')

        this.tts.speak(speakText, {
          priority: 'immediate',
          onCompleted: () => {
            if (promptStatus) {
              this.currentStack = ''
              this.$refs.inspectGuideRef.close()
            }
            this.checkTransitionReady()
          },
        })
      }

      if (promptStatus) {
        this.currentStack = 'inspectGuide'
        this.$refs.inspectGuideRef.open()
      }
    },
    onSocketMessage(res) {
      // 如果长时间没有收到消息则开启自动刷新机制
      clearInterval(this.refreshTimer)
      this.refreshTimer = setInterval(()=> {
        this.getInspectData()
      },15 * 1000)

      const itemId = res.itemNum
      const message = res.message || res.msg

      // 处理语音消息
      if (message && itemId) {
        const repeatFlag = this.tts.getQueue().queue.some(item=> item.text === message)
        if(repeatFlag) {
          return false
        }

        const outFlag = res.number < this.inspectInfo.number

        if(outFlag) {
          return false
        }

        // 如果正在切换检查项且消息属于新的检查项，暂存消息
        if (this.isTransitioning && itemId !== this.currentItemId) {
          this.pendingMessages.push({
              message,
              itemId,
            })
          return false
        }

        // 添加到播放队列
        this.addToTtsQueue(message, itemId)
      }

      // 处理检查项完成信号
      if (res.code === '200') {
        this.nextSocketMessage = res
        this.handleNext()
        return false
      }

      if (['201', '202'].includes(res.code)) {
        this.handleReport(res)
      }
    },
    async handleReport(res) {
      this.$refs.inspectAbnormalRef.open({
        image: res.debugImageUrl,
        content: res.message,
        socketMessage: res
      })

      this.currentStack = 'inspectAbnormal'
    },
    async getInspectData(options) {
      const res = await getVehicleItemGetCurrentInfo().send(true)

      if(!options?.manual) {
        this.inspectInfo = res.data
      }

      return res.data
    },
    async handleForcedStop() {
      const ret = await uni.showModal({
        title: '提示',
        content: '确定要结束当前流程吗',
      })

      if (ret.cancel) {
        return false
      }

      const params = {
        taskId: this.inspectInfo.taskId,
        itemNum: this.inspectInfo.itemNum,
      }

      await postVehicleItemUpdate(params)

      this.nextSocketMessage = void 0
      this.actualHandleNext()
    },
    async handleTaskDone() {
      const ret = await uni.showModal({
        title: '提示',
        content: '确定要完成当前任务吗',
      })

      if (ret.cancel) {
        return false
      }

      const params = {
        taskId: this.inspectInfo.taskId,
        itemNum: this.inspectInfo.itemNum,
      }

      await postVehicleTaskDone(params)

      uni.redirectTo({ url: `/pages/vehicle/inspect-result/index?taskId=${this.inspectInfo.taskId}` })
    },
    handleNext() {
      // 标记为正在切换状态，等待播放完成
      this.isTransitioning = true
      
      const hasCurrentItemMessages = this.hasCurrentItemMessages()
      // 检查是否还有当前检查项的消息未播放完成
      if (hasCurrentItemMessages) {
        return false
      }

      // 直接执行切换
      this.actualHandleNext()
    },

    addPendingToTtsQueue() {
      if (this.pendingMessages.length > 0) {
        this.pendingMessages.forEach((msg) => {
          this.addToTtsQueue(msg.message, msg.itemId)
        })
        this.pendingMessages = []
      }
    },

    async actualHandleNext() {
      // if (this.timer) {
      //   return false
      // }

      if(this.nextSocketMessage) {
        uni.$emit('socket-send', {
          uri: 'vehicleCompleteItem',
          token: this.userStore.userInfo.apiAccessToken,
          params: { ...this.nextSocketMessage },
        })
        this.nextSocketMessage = null
        await sleep()
      }

      const nextInspectInfo = await this.getInspectData({ manual: true })

      this.inspectInfo = { ...nextInspectInfo }
      this.onInspectGuide()
      this.addPendingToTtsQueue()

      // this.nextCountdown = 1

      // this.timer = setInterval(() => {
      //   if(this.nextCountdown === 1) {
      //     this.inspectInfo = { ...nextInspectInfo}
      //     this.onInspectGuide()
      //     this.addPendingToTtsQueue()
      //   }

      //   if (this.nextCountdown) {
      //     --this.nextCountdown
      //     return false
      //   }

      //   clearInterval(this.timer)
      //   this.timer = void 0
      // }, 1000)
    },
  },
}
</script>

<template>
  <app-layout-native :prompts="prompts">
    <template #inset>
      <app-live-pusher ref="pusherRef" :path="pusherPath" custom-class="live-pusher" v-model:pushing="pushing" />
      <view class="inspect-page__background"></view>
    </template>

    <template #header-left>
      <view class="header-left">
        <image
          v-if="pushing"
          src="~@assets/images/common/recording.gif"
          mode="aspectFit"
          class="header-left__live"
        />
        <text class="header-left__text">
          {{ inspectInfo?.workTicketNoOri }}
        </text>
      </view>
    </template>

    <template #header-center>
      <view v-if="inspectInfo" class="header-center">
        <view class="header-center__index">
          <text class="header-center__index-text">
            {{ inspectInfo.number }}
          </text>
        </view>

        <view v-if="nextCountdown" class="header-center__content">
          <text  class="header-center__text-content">
            {{ nextCountdown ? `即将进入下一个检修项(${nextCountdown})` : inspectTitle }}
          </text>
        </view>

        <image
          v-if="inspectInfo.hyzShow"
          src="~@assets/images/vehicle/icon-oil.png"
          mode="aspectFit"
          class="header-center__icon"
        />
        <image
          v-if="inspectInfo.lsShow"
          src="~@assets/images/vehicle/icon-screw.png"
          mode="aspectFit"
          class="header-center__icon"
        />
        <image
          v-if="inspectInfo.xlsShow"
          src="~@assets/images/vehicle/icon-touch.png"
          mode="aspectFit"
          class="header-center__icon"
        />
        <image
          v-if="inspectInfo.dlsShow"
          src="~@assets/images/vehicle/icon-hammer.png"
          mode="aspectFit"
          class="header-center__icon"
        />
      </view>
    </template>

    <view class="inspect-page">
      <view class="inspect-page__actions">
        <view
          v-for="(item, index) of actionModel"
          :key="index"
          class="inspect-page__action-item"
          :aria-label="item.label"
          @click="item.click(item)"
        >
          <image
            :src="item.icon"
            mode="aspectFit"
            class="inspect-page__action-icon"
          />
          <text
            class="inspect-page__action-text"
            :style="{ color: item.color || '#FFFFFF' }"
          >
            {{ item.label }}
          </text>
        </view>
      </view>

      <!-- 底部信息卡片 -->
      <view class="inspect-page__info-card">
        <!-- <view class="inspect-page__info-header">
          <view class="inspect-page__info-title">
            <text class="inspect-page__info-title-text">
              {{ inspectTitle }}
            </text>
          </view>
        </view> -->

        <view class="inspect-page__info-content">
          <text class="inspect-page__info-description">
            {{ inspectInfo?.standardProcess || '-' }}
          </text>
        </view>
      </view>
    </view>

    <InspectGuide ref="inspectGuideRef" v-bind="{ inspectInfo, inspectTitle }" />
    <InspectDiagram ref="inspectDiagramRef" v-bind="{ inspectInfo }" />
    <InspectPoint ref="inspectPointRef" v-bind="{ inspectInfo, inspectTitle }" />
    <InspectAbnormal ref="inspectAbnormalRef" />
  </app-layout-native>
</template>

<style>
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.header-left {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.header-left__live {
  width: 20rpx; 
  height: 20rpx;
  margin-right: 8rpx;
}

.header-left__text {
  color: #FFFFFF;
  lines: 1;
  text-overflow: ellipsis;
  flex: 1;
}

.header-center {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.header-center__index {
  min-width: 24rpx;
  height: 24rpx;
  border-radius: 999px;
  background-color: #ff8411;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8rpx;
}

.header-center__index-text {
  color: #1f2937;
  font-size: 14rpx;
  font-weight: bold;
  line-height: 24rpx;
  lines: 1;
  text-overflow: ellipsis;
}

.header-center__content {
  max-width: 220rpx;
}

.header-center__text-content {
  flex: 1;
  color: #ffffff;
  lines: 1;
  text-overflow: ellipsis;
}

.header-center__icon {
  width: 20rpx;
  height: 20rpx;
  margin-left: 8rpx;
}

.live-pusher {
  @apply absolute inset-0;
}

.inspect-page__background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.1);
}

.inspect-page {
  @apply absolute inset-0;
}

.inspect-page__actions {
  position: absolute;
  left: 0;
  top: 0;
  flex-direction: column;
}

.inspect-page__action-item {
  flex-direction: row;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  border-top-right-radius: 50rpx;
  border-bottom-right-radius: 50rpx;
  padding-left: 12rpx;
  padding-top: 8rpx;
  padding-bottom: 8rpx;
  padding-right: 12rpx;
  margin-top: 12rpx;
}

.inspect-page__action-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 8rpx;
}

.inspect-page__action-text {
  font-size: 20rpx;
  color: #ffffff;
}

.inspect-page__info-card {
  position: absolute;
  left: 24rpx;
  right: 24rpx;
  bottom: 12rpx;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 12rpx;
  border-radius: 8rpx;
}

.inspect-page__info-header {
  flex-direction: row;
  align-items: center;
}

.inspect-page__info-badge {
  background-color: rgba(255, 132, 17, 0.1);
  padding-left: 12rpx;
  padding-right: 12rpx;
  padding-top: 4rpx;
  padding-bottom: 4rpx;
  border-radius: 4rpx;
  margin-right: 16rpx;
}

.inspect-page__info-badge-text {
  color: #ff8411;
  font-size: 20rpx;
}

.inspect-page__info-title {
  flex: 1;
  margin-bottom: 12rpx;
}

.inspect-page__info-title-text {
  font-size: 24rpx;
  color: #ffffff;
  lines: 1;
  text-overflow: ellipsis;
}

.inspect-page__info-content {

}

.inspect-page__info-description {
  color: #d1d5db;
  font-size: 20rpx;
  lines: 3;
  text-overflow: ellipsis;
  line-height: 26rpx;
}
</style>
