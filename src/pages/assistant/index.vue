<script setup>
import { postVoiceAssistantUsageRecord } from '@/api/assistant/index.js'
import { VoiceAssistantStreamRenderjs } from '@/components/sse-stream/index.js'
import SSEStream from '@/components/sse-stream/sse-stream.vue'
import { primaryColor } from '@/settings/index.mjs'
import Conversation from './components/conversation/index.vue'

const userStore = useUserStore()

const tts = useXfyunTTS()

// 使用新的基于 renderjs 的 SSE 流处理
const sseOptions = {
  baseUrl: process.env.VITE_API_COPILOT,
  // dialogId: '0d829f6c5d3811f08f180242ac1c0006',
  dialogId: '53b51396740a11f0a98b0242ac120006',
  headers: {
    Authorization: 'Bearer ragflow-E1ODg0MDJhNzRjZTExZjA4ZWU2MDI0Mm',
  },
}

const voiceAssistantStream = new VoiceAssistantStreamRenderjs(sseOptions)

// SSE 组件引用
const sseStreamRef = ref(null)

// 语音识别钩子
const {
  isRecognizing,
  isSupported,
  partialResult,
  finalResult,
  volume,
  error: speechError,
  initialize: initializeSpeech,
  startRecognize,
  stopRecognize,
  clearResults,
  cleanup: cleanupSpeech,
} = useSpeechRecognitionPlus()

const waking = ref(false)
const question = ref('')
const answer = ref('')
const isStreaming = ref(false)
const streamError = ref('')

const conversationList = ref([
  {
    role: 'answer',
    content: `您好！我是AR巡检智能语音助手，您可以语音说出您的问题`,
  },
])

const conversationRef = ref()
const conversationVisible = ref(false)
const focusFlag = ref(false)

const scrollIntoView = ref('scroll-into-view')

const { scrollToBottom } = useScrollView({
  scrollIntoView,
})

// 计算属性
const logoStyle = computed(() => {
  let value = ''
  if (waking.value || isStreaming.value) {
    value = 'size-5'
  }
  else if (answer.value) {
    value = 'size-8'
  }
  else {
    value = 'size-14'
  }
  return value
})

const prompts = computed(() => {
  const value = []

  if (!waking.value) {
    value.push({
      label: isSupported.value ? '你好小安' : '开始输入',
      action: () => {
        onStartClick()
      },
    })
  }
  else {
    value.push({
      label: isSupported.value ? '停止监听' : '停止输入',
      action: () => {
        onStopClick()
      },
    })
  }
  return value
})

// 显示的问题文本（优先显示语音识别结果）
const displayQuestion = computed({
  get() {
    if (isRecognizing.value) {
      return partialResult.value || finalResult.value || question.value
    }
    return question.value
  },
  set(value) {
    question.value = value
  },
})

// 占位符文本
const speechPlaceholder = computed(() => {
  if (!isSupported.value) {
    return '请输入你的问题...'
  }
  if (isRecognizing.value) {
    return '正在监听中，请说话...'
  }
  return '点击开始监听或直接输入问题'
})

// 状态文本
const statusText = computed(() => {
  if (isStreaming.value) {
    return '正在思考...'
  }
  if (isRecognizing.value) {
    return '正在听...'
  }
  if (waking.value) {
    return '准备中...'
  }
  return ''
})

// 显示的错误信息
const displayError = computed(() => {
  return speechError.value?.message || streamError.value
})

// 监听语音识别结果
watch(finalResult, (newResult) => {
  if (newResult && waking.value) {
    question.value = newResult
    onStopClick()
  }
})

// 监听语音识别错误
watch(speechError, (error) => {
  if (error) {
    console.error('语音识别错误:', error)
    uni.showToast({
      title: error.message,
      icon: 'none',
      duration: 3000,
    })
    onStopClick()
  }
})

onLoad(async () => {
  initializeSpeech()

  // 初始化 SSE 组件连接
  await nextTick()
  if (sseStreamRef.value) {
    voiceAssistantStream.setSSEComponent(sseStreamRef.value)
    console.log('SSE 组件已设置:', sseStreamRef.value)
  }
})

onUnload(() => {
  voiceAssistantStream.stopConversation()
  cleanupSpeech()
})

async function onStartClick() {
  tts.speak('在', { priority: 'replace' })

  voiceAssistantStream.stopConversation()

  answer.value = ''
  waking.value = true
  isStreaming.value = false
  streamError.value = ''
  speechError.value = void 0

  clearResults()

  // 如果支持语音识别，启动语音识别
  if (isSupported.value) {
    try {
      await startRecognize()
    }
    catch (error) {
      console.error('启动语音识别失败:', error)
    }
  }
  else {
    // 不支持语音识别时，直接进入手动输入模式
    handleFocus()
  }
}

function onStopClick() {
  // 停止语音识别
  if (isRecognizing.value) {
    stopRecognize()
  }

  waking.value = false

  // 获取最终的问题文本
  const questionText = finalResult.value || question.value.trim()

  if (!questionText) {
    uni.showToast({
      title: isSupported.value ? '没有监听到问题内容' : '请输入问题内容',
      icon: 'none',
    })
    return false
  }

  isStreaming.value = true
  startStreamConversation(questionText)
}

// 开始流式对话
async function startStreamConversation(questionText) {
  try {
    // 添加问题到对话列表
    conversationList.value.push({
      role: 'question',
      content: questionText,
    })

    // 添加空的回答占位
    const answerIndex = conversationList.value.length
    conversationList.value.push({
      role: 'answer',
      content: '',
    })

    let accumulatedAnswer = ''

    voiceAssistantStream.startConversation({
      question: questionText,
      stream: true,
      onMessage: (messageData) => {
        if (messageData.answer) {
          waking.value = false
          accumulatedAnswer = messageData.answer
          answer.value = accumulatedAnswer

          // 更新对话列表中的回答
          if (conversationList.value[answerIndex]) {
            conversationList.value[answerIndex].content = accumulatedAnswer
          }

          scrollToBottom()
        }
      },
      onError: (error) => {
        streamError.value = error.message || '请求失败'
        waking.value = false
        isStreaming.value = false

        uni.showToast({
          title: streamError.value,
          icon: 'none',
          duration: 3000,
        })
      },
      onComplete: () => {
        waking.value = false
        isStreaming.value = false
        question.value = ''
        clearResults()

        console.log('对话完成，最终回答:', accumulatedAnswer)

        postVoiceAssistantUsageRecord({
          userName: userStore.workerInfo?.userName,
          problem: questionText,
          response: accumulatedAnswer,
          chatsId: voiceAssistantStream.options.dialogId,
          conversationId: voiceAssistantStream.sessionId,
          stationId: userStore.workerInfo.stationId,
          stationName: userStore.workerInfo.stationName,
        }).send()

        tts.stopPlay()
        tts.speak(accumulatedAnswer)
      },
    })
  }
  catch (error) {
    console.error('Start conversation error:', error)
    streamError.value = error.message || '启动对话失败'
    waking.value = false
    isStreaming.value = false

    uni.showToast({
      title: streamError.value,
      icon: 'none',
    })
  }
}

async function handleFocus() {
  focusFlag.value = false
  await nextTick()
  focusFlag.value = true
}

function onConversationClick() {
  conversationRef.value.toggle()

  if (!isSupported.value) {
    handleFocus()
  }
}
</script>

<template>
  <app-layout v-bind="{ prompts }">
    <template #inset>
      <image
        src="~@assets/images/assistant/bg-gradient.png"
        mode="aspectFill"
        class="absolute inset-0 size-full"
      />
    </template>
    <template #header-center>
      <view
        class="flex items-center justify-center h-full space-x-1"
        :aria-label="conversationVisible ? '关闭字幕' : '打开字幕'"
        @click="onConversationClick"
      >
        <view class="i-carbon-assignment-action-usage size-4"></view>
        <view class="text-xs">
          {{ conversationVisible ? '关闭字幕' : '打开字幕' }}
        </view>
      </view>
    </template>

    <view class="flex flex-col items-center justify-center h-full py-2">
      <!-- 输入区域 -->
      <view
        class="h-0 w-full overflow-hidden transition-all"
        :class="waking || conversationVisible || (!answer && isStreaming) ? 'flex-1 px-4 pt-4' : 'flex-none'"
      >
        <textarea
          v-if="!conversationVisible"
          v-model="displayQuestion"
          :focus="focusFlag"
          :placeholder="speechPlaceholder"
          cursor-color="#3BFFF8"
          class="text-base size-full text-center"
          placeholder-class="!text-[#727C94]"
          :disabled="isRecognizing"
          @confirm="onStartClick"
        ></textarea>
      </view>

      <!-- 助手图标和状态 -->
      <view
        class="flex-none flex items-center space-x-1 relative transition-all animate-pulse"
        :class="conversationVisible && (waking || isStreaming) ? 'z-50' : ''"
      >
        <image
          src="~@assets/images/assistant/icon-assistant.png"
          mode="aspectFit"
          class="flex-none transition-all"
          :class="[logoStyle]"
        />

        <view v-if="waking || isStreaming" class="text-[#727C94] text-2xs">
          {{ statusText }}
        </view>
      </view>

      <!-- 音量指示器 -->
      <!-- <view
        v-if="isRecognizing && volume > 0"
        class="flex-none mt-2 w-32 h-1 bg-gray-300 rounded-full overflow-hidden"
      >
        <view
          class="h-full bg-primary-400 transition-all duration-100"
          :style="{ width: `${volume * 100}%` }"
        ></view>
      </view> -->

      <!-- 回答显示区域 -->
      <scroll-view
        scroll-y
        scroll-with-animation
        class="h-0 px-8  overflow-hidden transition-all text-xs"
        :class="[
          !waking && answer && !conversationVisible ? 'flex-1 pt-2' : 'flex-none',
          answer.length > 30 ? 'text-left' : 'text-center',
        ]"
        :scroll-into-view="scrollIntoView"
      >
        <zero-markdown-view :markdown="answer" :theme-color="primaryColor">
        </zero-markdown-view>
        <view id="scroll-into-view"></view>
      </scroll-view>

      <!-- 错误提示 -->
      <view
        v-if="displayError"
        class="flex-none px-8 text-center text-red-500 text-sm mt-2"
      >
        {{ displayError }}
      </view>

      <!-- 欢迎信息 -->
      <view
        class="flex-none px-12 text-center text-[#727C94] text-sm overflow-hidden transition-all"
        :class="!waking && !answer && !displayError && !isStreaming ? 'pt-2' : 'h-0'"
      >
        <view>您好！我是AR巡检智能语音助手</view>
        <view>{{ isSupported ? '您可以语音说出您的问题' : '您可以输入您的问题' }}</view>
      </view>
    </view>

    <Conversation
      ref="conversationRef"
      v-model:focus-flag="focusFlag"
      v-model:waking="waking"
      v-model:visible="conversationVisible"
      v-model:question="question"
      v-model:conversation-list="conversationList"
      :is-streaming="isStreaming"
      :is-recognizing="isRecognizing"
      :volume="volume"
      :partial-result="partialResult"
      :final-result="finalResult"
      @send-message="startStreamConversation"
    />

    <!-- SSE 流处理组件 -->
    <SSEStream
      ref="sseStreamRef"
      :options="sseOptions"
    />
  </app-layout>
</template>

<style lang="postcss">
</style>
